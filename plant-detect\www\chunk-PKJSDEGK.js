import{a as K,n as Q,o as X,t as Z,v as nn,w as en,x as tn}from"./chunk-GQY3NJBS.js";import{b as J}from"./chunk-ZBJDNBYI.js";import"./chunk-YUFJ6257.js";import{Aa as E,C as a,Ca as F,D as g,E as P,F as p,G as c,H as x,I as e,Ia as T,J as t,K as s,Ka as L,La as z,M as u,Ma as G,N as b,Na as j,O as r,Q as C,Qa as D,Ua as N,Wa as B,Xa as W,ca as v,cb as q,fb as A,ga as M,gb as R,hb as V,mb as Y,na as O,nb as H,pb as U,qb as $,ta as h,ua as y,va as k,xa as w,ya as I,za as S}from"./chunk-DVLCYDDQ.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import{g as f}from"./chunk-OLRFWS6T.js";function rn(l,d){if(l&1&&(e(0,"ion-text",26),r(1),t()),l&2){let _=b();a(),C(" ",_.getFieldError("email")," ")}}function an(l,d){if(l&1&&(e(0,"ion-text",26),r(1),t()),l&2){let _=b();a(),C(" ",_.getFieldError("password")," ")}}function ln(l,d){l&1&&s(0,"ion-spinner",27)}function cn(l,d){l&1&&(e(0,"span"),r(1,"Sign In"),t())}function dn(l,d){l&1&&(e(0,"ion-card",28)(1,"ion-card-content")(2,"h4"),r(3,"Demo Credentials"),t(),e(4,"p")(5,"strong"),r(6,"Email:"),t(),r(7," <EMAIL>"),t(),e(8,"p")(9,"strong"),r(10,"Password:"),t(),r(11," demo123"),t(),e(12,"small"),r(13,"Use these credentials to test the app"),t()()())}var Pn=(()=>{let d=class d{constructor(o,n,i,m,on){this.formBuilder=o,this.authService=n,this.router=i,this.toastController=m,this.loadingController=on,this.showPassword=!1,this.isLoading=!1,K({mail:tn,lockClosed:nn,eye:Q,eyeOff:X,logoGoogle:en,leaf:Z}),this.loginForm=this.formBuilder.group({email:["",[h.required,h.email]],password:["",[h.required,h.minLength(6)]]})}ngOnInit(){this.authService.authState$.subscribe(o=>{this.isLoading=o.isLoading,o.isAuthenticated&&this.router.navigate(["/tabs/home"]),o.error&&this.showErrorToast(o.error)})}onSubmit(){return f(this,null,function*(){if(this.loginForm.valid){let{email:o,password:n}=this.loginForm.value;try{yield this.authService.signInWithEmail(o,n)}catch(i){console.error("Login error:",i)}}else this.markFormGroupTouched()})}signInWithGoogle(){return f(this,null,function*(){try{yield this.authService.signInWithGoogle()}catch(o){console.error("Google sign-in error:",o)}})}togglePasswordVisibility(){this.showPassword=!this.showPassword}navigateToRegister(){this.router.navigate(["/register"])}markFormGroupTouched(){Object.keys(this.loginForm.controls).forEach(o=>{let n=this.loginForm.get(o);n==null||n.markAsTouched()})}showErrorToast(o){return f(this,null,function*(){yield(yield this.toastController.create({message:o,duration:4e3,position:"top",color:"danger",buttons:[{text:"Dismiss",role:"cancel"}]})).present()})}isFieldInvalid(o){let n=this.loginForm.get(o);return!!(n&&n.invalid&&n.touched)}getFieldError(o){let n=this.loginForm.get(o);if(n&&n.errors&&n.touched){if(n.errors.required)return`${o.charAt(0).toUpperCase()+o.slice(1)} is required`;if(n.errors.email)return"Please enter a valid email address";if(n.errors.minlength)return"Password must be at least 6 characters long"}return""}};d.\u0275fac=function(n){return new(n||d)(g(E),g(J),g(O),g(H),g(Y))},d.\u0275cmp=P({type:d,selectors:[["app-login"]],decls:48,vars:16,consts:[[3,"translucent"],[1,"login-content",3,"fullscreen"],[1,"login-container"],[1,"app-header"],[1,"logo-container"],["name","leaf","size","large","color","primary"],[1,"login-card"],[3,"ngSubmit","formGroup"],["lines","none",1,"form-item"],["name","mail","slot","start","color","medium"],["position","stacked"],["type","email","formControlName","email","placeholder","Enter your email","autocomplete","email"],["color","danger","class","error-text",4,"ngIf"],["name","lock-closed","slot","start","color","medium"],["formControlName","password","placeholder","Enter your password","autocomplete","current-password",3,"type"],["fill","clear","slot","end",1,"password-toggle",3,"click"],["color","medium",3,"name"],["expand","block","type","submit",1,"signin-button",3,"disabled"],["name","crescent",4,"ngIf"],[4,"ngIf"],[1,"divider"],["expand","block","fill","outline",1,"google-button",3,"click","disabled"],["name","logo-google","slot","start"],[1,"signup-link"],["fill","clear","size","small",1,"link-button",3,"click"],["class","demo-card",4,"ngIf"],["color","danger",1,"error-text"],["name","crescent"],[1,"demo-card"]],template:function(n,i){n&1&&(e(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-title"),r(3,"Welcome Back"),t()()(),e(4,"ion-content",1)(5,"div",2)(6,"div",3)(7,"div",4),s(8,"ion-icon",5),t(),e(9,"h1"),r(10,"PlantDetect"),t(),e(11,"p"),r(12,"Discover and identify plants around you"),t()(),e(13,"ion-card",6)(14,"ion-card-header")(15,"ion-card-title"),r(16,"Sign In"),t()(),e(17,"ion-card-content")(18,"form",7),u("ngSubmit",function(){return i.onSubmit()}),e(19,"ion-item",8),s(20,"ion-icon",9),e(21,"ion-label",10),r(22,"Email"),t(),s(23,"ion-input",11),t(),p(24,rn,2,1,"ion-text",12),e(25,"ion-item",8),s(26,"ion-icon",13),e(27,"ion-label",10),r(28,"Password"),t(),s(29,"ion-input",14),e(30,"ion-button",15),u("click",function(){return i.togglePasswordVisibility()}),s(31,"ion-icon",16),t()(),p(32,an,2,1,"ion-text",12),e(33,"ion-button",17),p(34,ln,1,0,"ion-spinner",18)(35,cn,2,0,"span",19),t()(),e(36,"div",20)(37,"span"),r(38,"or"),t()(),e(39,"ion-button",21),u("click",function(){return i.signInWithGoogle()}),s(40,"ion-icon",22),r(41," Continue with Google "),t(),e(42,"div",23)(43,"p"),r(44," Don't have an account? "),e(45,"ion-button",24),u("click",function(){return i.navigateToRegister()}),r(46," Sign up here "),t()()()()(),p(47,dn,14,0,"ion-card",25),t()()),n&2&&(c("translucent",!0),a(4),c("fullscreen",!0),a(14),c("formGroup",i.loginForm),a(),x("item-has-error",i.isFieldInvalid("email")),a(5),c("ngIf",i.isFieldInvalid("email")),a(),x("item-has-error",i.isFieldInvalid("password")),a(4),c("type",i.showPassword?"text":"password"),a(2),c("name",i.showPassword?"eye-off":"eye"),a(),c("ngIf",i.isFieldInvalid("password")),a(),c("disabled",i.isLoading),a(),c("ngIf",i.isLoading),a(),c("ngIf",!i.isLoading),a(4),c("disabled",i.isLoading),a(8),c("ngIf",!i.isLoading))},dependencies:[M,v,F,w,y,k,I,S,D,N,R,V,L,G,j,z,B,W,$,T,U,A,q],styles:['.login-content[_ngcontent-%COMP%]{--background: linear-gradient(135deg, var(--ion-color-primary-tint), var(--ion-color-secondary-tint))}.login-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;min-height:100vh;padding:20px;justify-content:center}.app-header[_ngcontent-%COMP%]{text-align:center;margin-bottom:40px}.app-header[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]{margin-bottom:16px}.app-header[_ngcontent-%COMP%]   .logo-container[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{font-size:4rem;filter:drop-shadow(0 4px 8px rgba(0,0,0,.1))}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{margin:0 0 8px;font-size:2.5rem;font-weight:700;color:var(--ion-color-primary);text-shadow:0 2px 4px rgba(0,0,0,.1)}.app-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:1.1rem;font-weight:400}.login-card[_ngcontent-%COMP%]{border-radius:20px;box-shadow:0 10px 30px #0000001a;margin-bottom:20px;overflow:hidden}.login-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{background:var(--ion-color-light);text-align:center;padding:24px}.login-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]   ion-card-title[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:600;color:var(--ion-color-dark)}.login-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:24px}.form-item[_ngcontent-%COMP%]{--background: var(--ion-color-light);--border-radius: 12px;--padding-start: 16px;--padding-end: 16px;margin-bottom:16px;border:2px solid transparent;transition:all .3s ease}.form-item.item-has-error[_ngcontent-%COMP%]{--border-color: var(--ion-color-danger)}.form-item[_ngcontent-%COMP%]:focus-within{--border-color: var(--ion-color-primary);transform:translateY(-2px);box-shadow:0 4px 12px rgba(var(--ion-color-primary-rgb),.2)}.form-item[_ngcontent-%COMP%]   ion-label[_ngcontent-%COMP%]{font-weight:500;color:var(--ion-color-dark);margin-bottom:4px}.form-item[_ngcontent-%COMP%]   ion-input[_ngcontent-%COMP%]{--padding-top: 12px;--padding-bottom: 12px;font-size:1rem}.form-item[_ngcontent-%COMP%]   .password-toggle[_ngcontent-%COMP%]{--padding-start: 8px;--padding-end: 8px;margin:0}.error-text[_ngcontent-%COMP%]{display:block;font-size:.875rem;margin:-12px 0 16px 16px;font-weight:500}.signin-button[_ngcontent-%COMP%]{--background: var(--ion-color-primary);--border-radius: 12px;--box-shadow: 0 4px 12px rgba(var(--ion-color-primary-rgb), .3);height:56px;font-weight:600;font-size:1.1rem;margin:24px 0 16px;transition:all .3s ease}.signin-button[_ngcontent-%COMP%]:hover:not([disabled]){--background: var(--ion-color-primary-shade);transform:translateY(-2px);--box-shadow: 0 6px 16px rgba(var(--ion-color-primary-rgb), .4)}.signin-button[disabled][_ngcontent-%COMP%]{opacity:.6}.signin-button[_ngcontent-%COMP%]   ion-spinner[_ngcontent-%COMP%]{margin-right:8px}.divider[_ngcontent-%COMP%]{text-align:center;margin:24px 0;position:relative}.divider[_ngcontent-%COMP%]:before{content:"";position:absolute;top:50%;left:0;right:0;height:1px;background:var(--ion-color-light-shade)}.divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:#fff;padding:0 16px;color:var(--ion-color-medium);font-weight:500;position:relative;z-index:1}.google-button[_ngcontent-%COMP%]{--border-color: var(--ion-color-light-shade);--border-radius: 12px;--color: var(--ion-color-dark);height:56px;font-weight:500;transition:all .3s ease}.google-button[_ngcontent-%COMP%]:hover:not([disabled]){--background: var(--ion-color-light);transform:translateY(-1px);--box-shadow: 0 4px 12px rgba(0, 0, 0, .1)}.google-button[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%]{margin-right:8px}.signup-link[_ngcontent-%COMP%]{text-align:center;margin-top:24px}.signup-link[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0;color:var(--ion-color-medium);font-size:.95rem}.signup-link[_ngcontent-%COMP%]   .link-button[_ngcontent-%COMP%]{--color: var(--ion-color-primary);--padding-start: 4px;--padding-end: 4px;font-weight:600;text-decoration:underline}.demo-card[_ngcontent-%COMP%]{border-radius:12px;background:#ffffffe6;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.demo-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{text-align:center;padding:16px}.demo-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{margin:0 0 12px;color:var(--ion-color-primary);font-weight:600}.demo-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:4px 0;font-family:Courier New,monospace;font-size:.9rem;color:var(--ion-color-dark)}.demo-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{color:var(--ion-color-medium);font-style:italic}@media (prefers-color-scheme: dark){.login-content[_ngcontent-%COMP%]{--background: linear-gradient(135deg, var(--ion-color-primary-shade), var(--ion-color-secondary-shade))}.login-card[_ngcontent-%COMP%]{background:var(--ion-color-dark)}.login-card[_ngcontent-%COMP%]   ion-card-header[_ngcontent-%COMP%]{background:var(--ion-color-dark-tint)}.form-item[_ngcontent-%COMP%]{--background: var(--ion-color-dark-tint)}.divider[_ngcontent-%COMP%]:before{background:var(--ion-color-dark-shade)}.divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background:var(--ion-color-dark)}.demo-card[_ngcontent-%COMP%]{background:rgba(var(--ion-color-dark-rgb),.9)}}@media (max-width: 768px){.login-container[_ngcontent-%COMP%]{padding:16px}.app-header[_ngcontent-%COMP%]{margin-bottom:32px}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:2rem}.app-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem}.login-card[_ngcontent-%COMP%]   ion-card-content[_ngcontent-%COMP%]{padding:20px}}@media (max-height: 700px){.login-container[_ngcontent-%COMP%]{justify-content:flex-start;padding-top:40px}.app-header[_ngcontent-%COMP%]{margin-bottom:24px}}']});let l=d;return l})();export{Pn as LoginPage};
