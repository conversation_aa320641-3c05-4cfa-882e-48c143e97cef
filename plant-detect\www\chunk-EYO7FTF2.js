import{a as ie}from"./chunk-NTAPKKHP.js";import{E as ee,H as te,a as K,d as Q,e as U,k as W,l as X,t as Y,u as Z}from"./chunk-GQY3NJBS.js";import"./chunk-ZBJDNBYI.js";import"./chunk-YUFJ6257.js";import{C as o,D as g,E as y,F as b,Fa as V,G as c,I as i,Ia as j,J as n,Ja as L,K as p,Ka as B,L as h,La as F,M as _,N as d,O as a,Oa as M,P as v,Q as I,Qa as N,R as S,Ua as A,Va as H,X as O,Y as C,ba as E,ca as D,cb as R,da as w,ga as T,gb as z,hb as $,la as k,mb as q,na as P,nb as G,pb as J,v as x,w as u}from"./chunk-DVLCYDDQ.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import{g as f}from"./chunk-OLRFWS6T.js";function ae(t,s){if(t&1){let e=h();i(0,"ion-buttons",8)(1,"ion-button",9),_("click",function(){x(e);let l=d();return u(l.shareObservation())}),p(2,"ion-icon",10),n()()}}function re(t,s){t&1&&(i(0,"div",11),p(1,"ion-spinner",12),i(2,"p"),a(3,"Loading observation..."),n()())}function se(t,s){if(t&1){let e=h();i(0,"div",13)(1,"ion-card")(2,"ion-card-content")(3,"div",14),p(4,"ion-icon",15),i(5,"h3"),a(6,"Error Loading Observation"),n(),i(7,"p"),a(8),n(),i(9,"ion-button",16),_("click",function(){x(e);let l=d();return u(l.loadObservation())}),a(10," Try Again "),n()()()()()}if(t&2){let e=d();o(8),v(e.error)}}function le(t,s){if(t&1&&(i(0,"div",36)(1,"p"),a(2),n()()),t&2){let e=d(2);o(2),v(e.observation.species.description)}}function ce(t,s){if(t&1&&(i(0,"div",36)(1,"strong"),a(2,"Habitat:"),n(),a(3),n()),t&2){let e=d(2);o(3),I(" ",e.observation.species.habitat," ")}}function de(t,s){if(t&1&&(i(0,"div",26),p(1,"ion-icon",37),i(2,"span"),a(3),n()()),t&2){let e=d(2);o(3),v(e.observation.location.address||"Location recorded")}}function me(t,s){if(t&1&&(i(0,"div",38)(1,"strong"),a(2,"Notes:"),n(),i(3,"p",39),a(4),n()()),t&2){let e=d(2);o(4),v(e.observation.notes)}}function pe(t,s){if(t&1&&(i(0,"ion-chip",42),a(1),n()),t&2){let e=s.$implicit;o(),I(" ",e," ")}}function ve(t,s){if(t&1&&(i(0,"div",38)(1,"strong"),a(2,"Tags:"),n(),i(3,"div",40),b(4,pe,2,1,"ion-chip",41),n()()),t&2){let e=d(2);o(4),c("ngForOf",e.observation.tags)}}function ge(t,s){if(t&1){let e=h();i(0,"div",17)(1,"ion-card"),p(2,"ion-img",18),n(),i(3,"ion-card")(4,"ion-card-content")(5,"div",19)(6,"div",20)(7,"h2",21),a(8),n(),i(9,"p",22),a(10),n()(),i(11,"ion-chip",23),a(12),n()(),b(13,le,3,1,"div",24)(14,ce,4,1,"div",24),n()(),i(15,"ion-card")(16,"ion-card-content")(17,"h3",25),a(18,"Observation Details"),n(),i(19,"div",26),p(20,"ion-icon",27),i(21,"span"),a(22),n()(),i(23,"div",26),p(24,"ion-icon",28),i(25,"span"),a(26),O(27,"titlecase"),n()(),b(28,de,4,1,"div",29)(29,me,5,1,"div",30)(30,ve,5,1,"div",30),n()(),i(31,"ion-card")(32,"ion-card-content")(33,"div",31)(34,"ion-button",32),_("click",function(){x(e);let l=d();return u(l.editObservation())}),p(35,"ion-icon",33),a(36," Edit "),n(),i(37,"ion-button",34),_("click",function(){x(e);let l=d();return u(l.deleteObservation())}),p(38,"ion-icon",35),a(39," Delete "),n()()()()()}if(t&2){let e=d();o(2),c("src",e.observation.image.url||"/assets/placeholder-plant.jpg")("alt",e.observation.species.name),o(6),v(e.observation.species.name),o(2),I(" ",e.observation.species.scientificName," "),o(),c("color",e.getConfidenceColor(e.observation.species.confidence)),o(),I(" ",e.observation.species.confidence,"% "),o(),c("ngIf",e.observation.species.description),o(),c("ngIf",e.observation.species.habitat),o(8),v(e.formatDate(e.observation.createdAt)),o(4),S("",C(27,14,e.observation.plantPart)," \u2022 ",e.observation.floraRegion,""),o(2),c("ngIf",e.observation.location),o(),c("ngIf",e.observation.notes),o(),c("ngIf",e.observation.tags&&e.observation.tags.length>0)}}var Oe=(()=>{let s=class s{constructor(r,l,m,ne,oe){this.route=r,this.router=l,this.observationService=m,this.loadingController=ne,this.toastController=oe,this.observation=null,this.isLoading=!1,this.error=null,this.observationId=null,K({construct:W,arrowBack:Q,location:Z,calendar:U,leaf:Y,share:ee,trash:te,create:X})}ngOnInit(){this.observationId=this.route.snapshot.paramMap.get("id"),this.observationId&&this.loadObservation()}loadObservation(){return f(this,null,function*(){if(this.observationId){this.isLoading=!0,this.error=null;try{this.observation=yield this.observationService.getObservation(this.observationId),this.observation||(this.error="Observation not found")}catch(r){this.error=r.message||"Failed to load observation"}finally{this.isLoading=!1}}})}getConfidenceColor(r){return r>=80?"success":r>=60?"warning":"danger"}formatDate(r){return r?(r instanceof Date?r:r.toDate()).toLocaleDateString():""}shareObservation(){return f(this,null,function*(){yield(yield this.toastController.create({message:"Share feature coming soon!",duration:2e3,position:"bottom"})).present()})}editObservation(){return f(this,null,function*(){yield(yield this.toastController.create({message:"Edit feature coming soon!",duration:2e3,position:"bottom"})).present()})}deleteObservation(){return f(this,null,function*(){yield(yield this.toastController.create({message:"Delete feature coming soon!",duration:2e3,position:"bottom"})).present()})}};s.\u0275fac=function(l){return new(l||s)(g(k),g(P),g(ie),g(q),g(G))},s.\u0275cmp=y({type:s,selectors:[["app-observation-detail"]],decls:11,vars:7,consts:[[3,"translucent"],["slot","start"],["defaultHref","/tabs/observations"],["slot","end",4,"ngIf"],[3,"fullscreen"],["class","loading-container",4,"ngIf"],["class","error-container",4,"ngIf"],["style","padding: 20px;",4,"ngIf"],["slot","end"],["fill","clear",3,"click"],["name","share"],[1,"loading-container"],["name","crescent","color","primary"],[1,"error-container"],[2,"text-align","center","padding","20px"],["name","construct","size","large","color","danger"],["expand","block",3,"click"],[2,"padding","20px"],[2,"height","300px","object-fit","cover",3,"src","alt"],[2,"display","flex","justify-content","space-between","align-items","flex-start","margin-bottom","16px"],[2,"flex","1"],[2,"margin","0 0 8px 0"],[2,"margin","0","font-style","italic","color","var(--ion-color-medium)"],[3,"color"],["style","margin-bottom: 16px;",4,"ngIf"],[2,"margin","0 0 16px 0"],[2,"display","flex","align-items","center","margin-bottom","12px"],["name","calendar",2,"margin-right","12px","color","var(--ion-color-medium)"],["name","leaf",2,"margin-right","12px","color","var(--ion-color-medium)"],["style","display: flex; align-items: center; margin-bottom: 12px;",4,"ngIf"],["style","margin-top: 16px;",4,"ngIf"],[2,"display","flex","gap","12px"],["expand","block","fill","outline",3,"click"],["name","create","slot","start"],["expand","block","fill","outline","color","danger",3,"click"],["name","trash","slot","start"],[2,"margin-bottom","16px"],["name","location",2,"margin-right","12px","color","var(--ion-color-medium)"],[2,"margin-top","16px"],[2,"margin","8px 0 0 0"],[2,"margin-top","8px"],["color","tertiary",4,"ngFor","ngForOf"],["color","tertiary"]],template:function(l,m){l&1&&(i(0,"ion-header",0)(1,"ion-toolbar")(2,"ion-buttons",1),p(3,"ion-back-button",2),n(),i(4,"ion-title"),a(5),n(),b(6,ae,3,0,"ion-buttons",3),n()(),i(7,"ion-content",4),b(8,re,4,0,"div",5)(9,se,11,1,"div",6)(10,ge,40,16,"div",7),n()),l&2&&(c("translucent",!0),o(5),v((m.observation==null||m.observation.species==null?null:m.observation.species.name)||"Observation"),o(),c("ngIf",m.observation),o(),c("fullscreen",!0),o(),c("ngIf",m.isLoading),o(),c("ngIf",m.error&&!m.observation),o(),c("ngIf",m.observation&&!m.isLoading))},dependencies:[T,E,D,w,N,A,z,$,B,F,J,j,H,M,R,V,L],encapsulation:2});let t=s;return t})();export{Oe as ObservationDetailPage};
