import{b as O,c as U}from"./chunk-2DZQVLWT.js";import{a as $}from"./chunk-JBBAVVC3.js";import{b as m}from"./chunk-ZBJDNBYI.js";import{$ as L,B as w,J as x,Q as M,Y as D,t as j,u as k,z as T}from"./chunk-YUFJ6257.js";import{Da as y,E as u,Ea as I,Ga as F,I as l,J as d,K as f,ha as v,i as a,j as p,ja as g,jb as R,ka as A,m as c,ma as C,na as s,o as n,pa as b,qa as P,ra as S}from"./chunk-DVLCYDDQ.js";import"./chunk-MYOBYLQN.js";import"./chunk-64QR6YHT.js";import"./chunk-6DBQW3S3.js";import"./chunk-MMQ4D4FU.js";import"./chunk-JZIOZHPP.js";import"./chunk-R7ZYQ4ZY.js";import"./chunk-4U6PRYVA.js";import"./chunk-F33KLCOC.js";import"./chunk-JWIEPCRG.js";import"./chunk-C5RQ2IC2.js";import"./chunk-D4VGNZLR.js";import"./chunk-JHLW7V75.js";import"./chunk-SV7S5NYR.js";import"./chunk-OLRFWS6T.js";var i=(()=>{let e=class e{constructor(o,r){this.authService=o,this.router=r}canActivate(){return this.checkAuth()}canActivateChild(){return this.checkAuth()}checkAuth(){return this.authService.authState$.pipe(p(1),a(o=>o.isLoading||o.isAuthenticated?!0:this.router.createUrlTree(["/login"])))}};e.\u0275fac=function(r){return new(r||e)(n(m),n(s))},e.\u0275prov=c({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})(),h=(()=>{let e=class e{constructor(o,r){this.authService=o,this.router=r}canActivate(){return this.authService.authState$.pipe(p(1),a(o=>o.isLoading?!0:o.isAuthenticated?this.router.createUrlTree(["/tabs/home"]):!0))}};e.\u0275fac=function(r){return new(r||e)(n(m),n(s))},e.\u0275prov=c({token:e,factory:e.\u0275fac,providedIn:"root"});let t=e;return t})();var z=[{path:"",redirectTo:"/tabs/home",pathMatch:"full"},{path:"login",loadComponent:()=>import("./chunk-PKJSDEGK.js").then(t=>t.LoginPage),canActivate:[h]},{path:"register",loadComponent:()=>import("./chunk-M7H2Z6UP.js").then(t=>t.RegisterPage),canActivate:[h]},{path:"tabs",loadChildren:()=>import("./chunk-TVSRZH6P.js").then(t=>t.routes),canActivate:[i]},{path:"identify",loadComponent:()=>import("./chunk-YD43O6VE.js").then(t=>t.IdentifyPage),canActivate:[i]},{path:"results/:id",loadComponent:()=>import("./chunk-JPDV4UEH.js").then(t=>t.ResultsPage),canActivate:[i]},{path:"observation/:id",loadComponent:()=>import("./chunk-EYO7FTF2.js").then(t=>t.ObservationDetailPage),canActivate:[i]},{path:"observations",loadComponent:()=>import("./chunk-WHSZGQIQ.js").then(t=>t.ObservationsListPage),canActivate:[i]},{path:"map",loadComponent:()=>import("./chunk-SZFET73X.js").then(t=>t.MapPage),canActivate:[i]},{path:"flora",loadComponent:()=>import("./chunk-YR7HOGVW.js").then(t=>t.FloraSelectionPage),canActivate:[i]},{path:"profile",loadComponent:()=>import("./chunk-4TIPWRT2.js").then(t=>t.ProfilePage),canActivate:[i]},{path:"settings",loadComponent:()=>import("./chunk-RYOSI3NJ.js").then(t=>t.SettingsPage),canActivate:[i]},{path:"test-config",loadComponent:()=>import("./chunk-3YX2LTFF.js").then(t=>t.TestConfigPage),canActivate:[i]},{path:"**",redirectTo:"/tabs/home"}];var E=(()=>{let e=class e{constructor(){}};e.\u0275fac=function(r){return new(r||e)},e.\u0275cmp=u({type:e,selectors:[["app-root"]],decls:2,vars:0,template:function(r,q){r&1&&(l(0,"ion-app"),f(1,"ion-router-outlet"),d())},dependencies:[F,I],encapsulation:2});let t=e;return t})();v(E,{providers:[{provide:C,useClass:y},R(),P(z,S(b)),g(A()),j(()=>k($.firebaseConfig)),T(()=>w()),x(()=>M()),D(()=>L()),O(()=>U())]});
