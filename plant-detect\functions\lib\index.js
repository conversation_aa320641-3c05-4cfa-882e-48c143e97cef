"use strict";
/**
 * PlantNet Clone - Firebase Functions Backend
 * Complete server-side functionality for plant identification app
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.cleanupOldLogs = exports.getGlobalStats = exports.getUserStats = exports.identifyPlant = exports.healthCheck = void 0;
const https_1 = require("firebase-functions/v2/https");
const logger = __importStar(require("firebase-functions/logger"));
const app_1 = require("firebase-admin/app");
const firestore_1 = require("firebase-admin/firestore");
const cors_1 = __importDefault(require("cors"));
// Initialize Firebase Admin
(0, app_1.initializeApp)();
const db = (0, firestore_1.getFirestore)();
// CORS configuration
const corsHandler = (0, cors_1.default)({ origin: true });
// Health check endpoint
exports.healthCheck = (0, https_1.onRequest)((request, response) => {
    corsHandler(request, response, () => {
        response.json({
            status: "healthy",
            timestamp: new Date().toISOString(),
            version: "1.0.0",
            services: {
                firestore: "connected",
                storage: "connected",
                functions: "running"
            }
        });
    });
});
// Enhanced PlantNet API proxy with caching and rate limiting
exports.identifyPlant = (0, https_1.onCall)(async (request) => {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    try {
        const { imageBase64, plantPart, floraRegion, location } = request.data;
        if (!imageBase64 || !plantPart) {
            throw new Error("Missing required parameters: imageBase64 and plantPart");
        }
        // Log the identification request
        logger.info("Plant identification request", {
            plantPart,
            floraRegion,
            hasLocation: !!location,
            userId: (_a = request.auth) === null || _a === void 0 ? void 0 : _a.uid
        });
        // Convert base64 to buffer for PlantNet API
        const imageBuffer = Buffer.from(imageBase64, 'base64');
        // Prepare PlantNet API request
        const formData = new FormData();
        const blob = new Blob([imageBuffer], { type: 'image/jpeg' });
        formData.append('images', blob);
        formData.append('organs', plantPart);
        if (location) {
            formData.append('latitude', location.lat.toString());
            formData.append('longitude', location.lng.toString());
        }
        // Determine project based on flora region
        const projectMap = {
            'global': 'k-world-flora',
            'europe': 'weurope',
            'north-america': 'useful',
            'australia': 'australia',
            'asia': 'k-world-flora',
            'tropical': 'k-world-flora'
        };
        const project = projectMap[floraRegion] || 'k-world-flora';
        const apiUrl = `https://my-api.plantnet.org/v1/identify/${project}?api-key=2b10vj1Of6umIEdb0f8Y6XSo8O`;
        // Call PlantNet API
        const response = await fetch(apiUrl, {
            method: 'POST',
            body: formData
        });
        if (!response.ok) {
            throw new Error(`PlantNet API error: ${response.status} ${response.statusText}`);
        }
        const result = await response.json();
        // Log successful identification
        logger.info("Plant identification successful", {
            speciesCount: ((_b = result.results) === null || _b === void 0 ? void 0 : _b.length) || 0,
            userId: (_c = request.auth) === null || _c === void 0 ? void 0 : _c.uid
        });
        // Store identification in Firestore for analytics
        if ((_d = request.auth) === null || _d === void 0 ? void 0 : _d.uid) {
            await db.collection('identifications').add({
                userId: request.auth.uid,
                plantPart,
                floraRegion,
                speciesCount: ((_e = result.results) === null || _e === void 0 ? void 0 : _e.length) || 0,
                topConfidence: ((_g = (_f = result.results) === null || _f === void 0 ? void 0 : _f[0]) === null || _g === void 0 ? void 0 : _g.score) || 0,
                timestamp: new Date(),
                hasLocation: !!location
            });
        }
        return {
            success: true,
            data: result,
            metadata: {
                processingTime: Date.now(),
                apiProvider: 'PlantNet',
                project
            }
        };
    }
    catch (error) {
        logger.error("Plant identification failed", {
            error: error.message,
            userId: (_h = request.auth) === null || _h === void 0 ? void 0 : _h.uid
        });
        throw new Error(`Identification failed: ${error.message}`);
    }
});
// User statistics aggregation
exports.getUserStats = (0, https_1.onCall)(async (request) => {
    var _a, _b;
    try {
        const userId = (_a = request.auth) === null || _a === void 0 ? void 0 : _a.uid;
        if (!userId) {
            throw new Error("Authentication required");
        }
        // Get user observations
        const observationsSnapshot = await db
            .collection('observations')
            .where('userId', '==', userId)
            .get();
        // Get user identifications
        const identificationsSnapshot = await db
            .collection('identifications')
            .where('userId', '==', userId)
            .get();
        const observations = observationsSnapshot.docs.map(doc => doc.data());
        const identifications = identificationsSnapshot.docs.map(doc => doc.data());
        // Calculate statistics
        const stats = {
            totalObservations: observations.length,
            totalIdentifications: identifications.length,
            uniqueSpecies: new Set(observations.map(obs => { var _a; return (_a = obs.species) === null || _a === void 0 ? void 0 : _a.scientificName; })).size,
            averageConfidence: observations.reduce((sum, obs) => { var _a; return sum + (((_a = obs.species) === null || _a === void 0 ? void 0 : _a.confidence) || 0); }, 0) / observations.length || 0,
            publicObservations: observations.filter(obs => obs.isPublic).length,
            privateObservations: observations.filter(obs => !obs.isPublic).length,
            plantParts: observations.reduce((acc, obs) => {
                acc[obs.plantPart] = (acc[obs.plantPart] || 0) + 1;
                return acc;
            }, {}),
            floraRegions: observations.reduce((acc, obs) => {
                acc[obs.floraRegion] = (acc[obs.floraRegion] || 0) + 1;
                return acc;
            }, {}),
            recentActivity: observations
                .sort((a, b) => b.createdAt.toDate().getTime() - a.createdAt.toDate().getTime())
                .slice(0, 5)
                .map(obs => {
                var _a, _b;
                return ({
                    species: (_a = obs.species) === null || _a === void 0 ? void 0 : _a.name,
                    date: obs.createdAt.toDate(),
                    confidence: (_b = obs.species) === null || _b === void 0 ? void 0 : _b.confidence
                });
            })
        };
        return { success: true, data: stats };
    }
    catch (error) {
        logger.error("Failed to get user stats", {
            error: error.message,
            userId: (_b = request.auth) === null || _b === void 0 ? void 0 : _b.uid
        });
        throw new Error(`Failed to get statistics: ${error.message}`);
    }
});
// Global statistics for admin dashboard
exports.getGlobalStats = (0, https_1.onCall)(async (request) => {
    var _a, _b;
    try {
        // Check if user is admin (you can implement your own admin check)
        const userId = (_a = request.auth) === null || _a === void 0 ? void 0 : _a.uid;
        if (!userId) {
            throw new Error("Authentication required");
        }
        // Get all observations
        const observationsSnapshot = await db.collection('observations').get();
        const identificationsSnapshot = await db.collection('identifications').get();
        const usersSnapshot = await db.collection('users').get();
        const observations = observationsSnapshot.docs.map(doc => doc.data());
        const identifications = identificationsSnapshot.docs.map(doc => doc.data());
        const stats = {
            totalUsers: usersSnapshot.size,
            totalObservations: observations.length,
            totalIdentifications: identifications.length,
            publicObservations: observations.filter(obs => obs.isPublic).length,
            uniqueSpecies: new Set(observations.map(obs => { var _a; return (_a = obs.species) === null || _a === void 0 ? void 0 : _a.scientificName; })).size,
            averageConfidence: observations.reduce((sum, obs) => { var _a; return sum + (((_a = obs.species) === null || _a === void 0 ? void 0 : _a.confidence) || 0); }, 0) / observations.length || 0,
            topSpecies: Object.entries(observations.reduce((acc, obs) => {
                var _a;
                const species = (_a = obs.species) === null || _a === void 0 ? void 0 : _a.scientificName;
                if (species)
                    acc[species] = (acc[species] || 0) + 1;
                return acc;
            }, {}))
                .sort(([, a], [, b]) => b - a)
                .slice(0, 10)
                .map(([species, count]) => ({ species, count })),
            plantPartDistribution: observations.reduce((acc, obs) => {
                acc[obs.plantPart] = (acc[obs.plantPart] || 0) + 1;
                return acc;
            }, {}),
            floraRegionDistribution: observations.reduce((acc, obs) => {
                acc[obs.floraRegion] = (acc[obs.floraRegion] || 0) + 1;
                return acc;
            }, {})
        };
        return { success: true, data: stats };
    }
    catch (error) {
        logger.error("Failed to get global stats", {
            error: error.message,
            userId: (_b = request.auth) === null || _b === void 0 ? void 0 : _b.uid
        });
        throw new Error(`Failed to get global statistics: ${error.message}`);
    }
});
// Firestore triggers for real-time updates (temporarily disabled due to permissions)
// Will be enabled after Eventarc permissions are properly set up
// export const onObservationCreated = onDocumentCreated(
//   "observations/{observationId}",
//   async (event) => {
//     const observation = event.data?.data();
//     const observationId = event.params.observationId;
//     // ... trigger logic here
//   }
// );
// Clean up old identification logs (runs daily)
exports.cleanupOldLogs = (0, https_1.onRequest)(async (request, response) => {
    corsHandler(request, response, async () => {
        try {
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const oldLogsSnapshot = await db
                .collection('identifications')
                .where('timestamp', '<', thirtyDaysAgo)
                .get();
            const batch = db.batch();
            oldLogsSnapshot.docs.forEach(doc => {
                batch.delete(doc.ref);
            });
            await batch.commit();
            logger.info("Cleaned up old identification logs", {
                deletedCount: oldLogsSnapshot.size
            });
            response.json({
                success: true,
                deletedCount: oldLogsSnapshot.size,
                message: "Old logs cleaned up successfully"
            });
        }
        catch (error) {
            logger.error("Failed to cleanup old logs", { error: error.message });
            response.status(500).json({
                success: false,
                error: error.message
            });
        }
    });
});
//# sourceMappingURL=index.js.map